'use client';

import React, { useState, useEffect, useRef } from 'react';
import { PrerequisitesChecker } from './PrerequisitesChecker';
import { useParams } from 'next/navigation';
import {
  BookOpen,
  ChevronRight,
  ChevronLeft,
  ChevronDown,
  ChevronUp,
  Home,
  Download,
  Bookmark,
  MessageSquare,
  Edit3,
  Save,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  FileText,
  Link,
  Clock,
  CheckCircle,
  Circle,
  Star,
  Share2,
  Menu,
  X,
  Search,
  Filter,
  PlusCircle,
  Eye,
  EyeOff,
} from 'lucide-react';

export const EducationalCourseLayout = () => {
  const params = useParams();
  const courseId = params?.courseId as string;

  const [currentChapter, setCurrentChapter] = useState(0);
  const [currentLesson, setCurrentLesson] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [progress, setProgress] = useState({});
  const [bookmarks, setBookmarks] = useState(new Set());
  const [notes, setNotes] = useState({});
  const [currentNote, setCurrentNote] = useState('');
  const [showNotes, setShowNotes] = useState(false);
  const [showDiscussion, setShowDiscussion] = useState(false);
  const [showQuiz, setShowQuiz] = useState(false);
  const [videoPlaying, setVideoPlaying] = useState(false);
  const [videoMuted, setVideoMuted] = useState(false);
  const [offlineMode, setOfflineMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedText, setHighlightedText] = useState(new Set());
  const [showPrerequisites, setShowPrerequisites] = useState(true);
  const [prerequisitesMet, setPrerequisitesMet] = useState(false);
  const videoRef = useRef(null);
  const notesRef = useRef(null);

  // Course data structure
  const [courseData] = useState({
    title: 'Advanced Medical Anatomy',
    description: 'Comprehensive study of human anatomy for medical students',
    chapters: [
      {
        id: 1,
        title: 'Cardiovascular System',
        duration: '4 hours',
        lessons: [
          {
            id: 1,
            title: 'Heart Structure and Function',
            type: 'video',
            duration: '25 min',
            content: {
              video: '/api/video/heart-structure.mp4',
              transcript: 'The human heart is a four-chambered muscular organ...',
              slides: ['heart-anatomy-1.pdf', 'heart-function-2.pdf'],
            },
            completed: true,
          },
          {
            id: 2,
            title: 'Blood Circulation Pathways',
            type: 'interactive',
            duration: '30 min',
            content: {
              text: 'Blood circulation involves two main circuits: pulmonary and systemic circulation. The pulmonary circuit carries deoxygenated blood from the right ventricle to the lungs, where it picks up oxygen and releases carbon dioxide. The oxygenated blood then returns to the left atrium via the pulmonary veins.',
              interactive: true,
              diagrams: ['circulation-diagram.svg'],
            },
            completed: false,
          },
          {
            id: 3,
            title: 'Cardiac Cycle and ECG',
            type: 'text',
            duration: '20 min',
            content: {
              text: 'The cardiac cycle represents the sequence of events that occur when the heart beats. It consists of two major phases: systole (contraction) and diastole (relaxation). Understanding the ECG helps in interpreting the electrical activity of the heart during these phases.',
              images: ['ecg-normal.png', 'cardiac-cycle.gif'],
            },
            completed: false,
          },
        ],
      },
      {
        id: 2,
        title: 'Respiratory System',
        duration: '3.5 hours',
        lessons: [
          {
            id: 4,
            title: 'Lung Anatomy',
            type: 'video',
            duration: '30 min',
            content: {
              video: '/api/video/lung-anatomy.mp4',
              transcript:
                'The respiratory system consists of the airways, lungs, and respiratory muscles...',
            },
            completed: false,
          },
          {
            id: 5,
            title: 'Gas Exchange Mechanisms',
            type: 'interactive',
            duration: '35 min',
            content: {
              text: 'Gas exchange occurs in the alveoli through the process of diffusion. Oxygen moves from the alveolar air into the blood, while carbon dioxide moves from the blood into the alveolar air.',
              interactive: true,
            },
            completed: false,
          },
        ],
      },
    ],
    resources: [
      {
        id: 1,
        title: 'Anatomy Atlas',
        type: 'pdf',
        size: '15 MB',
        url: '/resources/anatomy-atlas.pdf',
      },
      { id: 2, title: 'Interactive 3D Models', type: 'link', url: 'https://3d-anatomy.com' },
      {
        id: 3,
        title: 'Practice Questions',
        type: 'pdf',
        size: '2 MB',
        url: '/resources/practice-questions.pdf',
      },
      {
        id: 4,
        title: 'Clinical Cases',
        type: 'pdf',
        size: '8 MB',
        url: '/resources/clinical-cases.pdf',
      },
    ],
    discussions: [
      {
        id: 1,
        user: 'Dr. Sarah Johnson',
        avatar: '/avatars/dr-johnson.jpg',
        time: '2 hours ago',
        message:
          'Great explanation of the cardiac cycle! For additional understanding, I recommend reviewing the pressure-volume loops.',
        replies: 3,
      },
      {
        id: 2,
        user: 'Mike Chen',
        avatar: '/avatars/mike.jpg',
        time: '1 day ago',
        message:
          'The ECG interpretation section was particularly helpful. Are there any recommended resources for practicing ECG reading?',
        replies: 1,
      },
    ],
  });

  // Initialize progress from localStorage
  useEffect(() => {
    const savedProgress = JSON.parse(localStorage.getItem('courseProgress') || '{}');
    const savedBookmarks = new Set(JSON.parse(localStorage.getItem('courseBookmarks') || '[]'));
    const savedNotes = JSON.parse(localStorage.getItem('courseNotes') || '{}');

    setProgress(savedProgress);
    setBookmarks(savedBookmarks);
    setNotes(savedNotes);

    // Check offline status
    setOfflineMode(!navigator.onLine);
  }, []);

  // Save progress to localStorage
  useEffect(() => {
    localStorage.setItem('courseProgress', JSON.stringify(progress));
  }, [progress]);

  useEffect(() => {
    localStorage.setItem('courseBookmarks', JSON.stringify([...bookmarks]));
  }, [bookmarks]);

  useEffect(() => {
    localStorage.setItem('courseNotes', JSON.stringify(notes));
  }, [notes]);

  // Online/offline detection
  useEffect(() => {
    const handleOnline = () => setOfflineMode(false);
    const handleOffline = () => setOfflineMode(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const getCurrentLesson = () => {
    const chapter = courseData.chapters[currentChapter];
    return chapter ? chapter.lessons[currentLesson] : null;
  };

  const markLessonComplete = (chapterId, lessonId) => {
    setProgress(prev => ({
      ...prev,
      [`${chapterId}-${lessonId}`]: true,
    }));
  };

  const toggleBookmark = () => {
    const lesson = getCurrentLesson();
    if (lesson) {
      const bookmarkId = `${currentChapter}-${currentLesson}`;
      setBookmarks(prev => {
        const newBookmarks = new Set(prev);
        if (newBookmarks.has(bookmarkId)) {
          newBookmarks.delete(bookmarkId);
        } else {
          newBookmarks.add(bookmarkId);
        }
        return newBookmarks;
      });
    }
  };

  const saveNote = () => {
    const lesson = getCurrentLesson();
    if (lesson && currentNote.trim()) {
      const noteId = `${currentChapter}-${currentLesson}`;
      setNotes(prev => ({
        ...prev,
        [noteId]: [
          ...(prev[noteId] || []),
          {
            id: Date.now(),
            text: currentNote,
            timestamp: new Date().toISOString(),
          },
        ],
      }));
      setCurrentNote('');
    }
  };

  const navigateLesson = direction => {
    const currentChapterData = courseData.chapters[currentChapter];
    if (direction === 'next') {
      if (currentLesson < currentChapterData.lessons.length - 1) {
        setCurrentLesson(currentLesson + 1);
      } else if (currentChapter < courseData.chapters.length - 1) {
        setCurrentChapter(currentChapter + 1);
        setCurrentLesson(0);
      }
    } else {
      if (currentLesson > 0) {
        setCurrentLesson(currentLesson - 1);
      } else if (currentChapter > 0) {
        setCurrentChapter(currentChapter - 1);
        setCurrentLesson(courseData.chapters[currentChapter - 1].lessons.length - 1);
      }
    }
  };

  const calculateProgress = () => {
    const totalLessons = courseData.chapters.reduce(
      (total, chapter) => total + chapter.lessons.length,
      0
    );
    const completedLessons = Object.keys(progress).length;
    return Math.round((completedLessons / totalLessons) * 100);
  };

  const Breadcrumb = () => {
    const lesson = getCurrentLesson();
    const chapter = courseData.chapters[currentChapter];

    return (
      <div className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
        <Home className="w-4 h-4" />
        <ChevronRight className="w-4 h-4" />
        <span>{courseData.title}</span>
        <ChevronRight className="w-4 h-4" />
        <span>{chapter?.title}</span>
        <ChevronRight className="w-4 h-4" />
        <span className="text-gray-900 font-medium">{lesson?.title}</span>
      </div>
    );
  };

  const ProgressBar = () => {
    const progressPercent = calculateProgress();
    return (
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">Course Progress</span>
          <span className="text-sm text-gray-500">{progressPercent}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercent}%` }}
          ></div>
        </div>
      </div>
    );
  };

  const VideoPlayer = ({ src }) => (
    <div className="relative bg-black rounded-lg overflow-hidden mb-6">
      <video
        ref={videoRef}
        className="w-full h-64 object-cover"
        poster="/video-thumbnail.jpg"
        controls={false}
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setVideoPlaying(!videoPlaying)}
              className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
            >
              {videoPlaying ? (
                <Pause className="w-5 h-5 text-white" />
              ) : (
                <Play className="w-5 h-5 text-white" />
              )}
            </button>
            <button
              onClick={() => setVideoMuted(!videoMuted)}
              className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
            >
              {videoMuted ? (
                <VolumeX className="w-5 h-5 text-white" />
              ) : (
                <Volume2 className="w-5 h-5 text-white" />
              )}
            </button>
          </div>
          <button className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors">
            <Maximize className="w-5 h-5 text-white" />
          </button>
        </div>
      </div>
    </div>
  );

  const ContentRenderer = () => {
    const lesson = getCurrentLesson();
    if (!lesson) return null;

    return (
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900">{lesson.title}</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleBookmark}
              className={`p-2 rounded-lg transition-colors ${
                bookmarks.has(`${currentChapter}-${currentLesson}`)
                  ? 'bg-yellow-100 text-yellow-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <Bookmark className="w-5 h-5" />
            </button>
            <button
              onClick={() => markLessonComplete(currentChapter + 1, lesson.id)}
              className={`p-2 rounded-lg transition-colors ${
                progress[`${currentChapter + 1}-${lesson.id}`]
                  ? 'bg-green-100 text-green-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <CheckCircle className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-4 mb-6 text-sm text-gray-500">
          <span className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>{lesson.duration}</span>
          </span>
          <span className="flex items-center space-x-1">
            <FileText className="w-4 h-4" />
            <span>{lesson.type}</span>
          </span>
          {offlineMode && (
            <span className="flex items-center space-x-1 text-orange-600">
              <EyeOff className="w-4 h-4" />
              <span>Offline Mode</span>
            </span>
          )}
        </div>

        {lesson.type === 'video' && lesson.content.video && (
          <VideoPlayer src={lesson.content.video} />
        )}

        {lesson.content.text && (
          <div className="prose max-w-none mb-6">
            <p className="text-gray-700 leading-relaxed">{lesson.content.text}</p>
          </div>
        )}

        {lesson.content.interactive && (
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 mb-3">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-blue-800">Interactive Content</span>
            </div>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Launch Interactive Module
            </button>
          </div>
        )}

        <div className="flex justify-between items-center mt-8">
          <button
            onClick={() => navigateLesson('prev')}
            disabled={currentChapter === 0 && currentLesson === 0}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Previous</span>
          </button>
          <button
            onClick={() => navigateLesson('next')}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <span>Next</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  };

  const Sidebar = () => (
    <div
      className={`${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden bg-white border-r border-gray-200`}
    >
      <div className="p-6">
        {/* Course Navigation */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Course Content</h3>
          <div className="space-y-2">
            {courseData.chapters.map((chapter, chapterIndex) => (
              <div key={chapter.id} className="border border-gray-200 rounded-lg">
                <button
                  className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
                  onClick={() => setCurrentChapter(chapterIndex)}
                >
                  <div>
                    <p className="font-medium text-gray-900">{chapter.title}</p>
                    <p className="text-sm text-gray-500">{chapter.duration}</p>
                  </div>
                  <ChevronRight className="w-4 h-4 text-gray-400" />
                </button>
                {currentChapter === chapterIndex && (
                  <div className="border-t border-gray-200 bg-gray-50">
                    {chapter.lessons.map((lesson, lessonIndex) => (
                      <button
                        key={lesson.id}
                        onClick={() => setCurrentLesson(lessonIndex)}
                        className={`w-full flex items-center justify-between p-3 text-left hover:bg-gray-100 ${
                          currentLesson === lessonIndex
                            ? 'bg-blue-50 border-l-2 border-blue-500'
                            : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          {progress[`${chapterIndex + 1}-${lesson.id}`] ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <Circle className="w-4 h-4 text-gray-400" />
                          )}
                          <div>
                            <p className="text-sm font-medium text-gray-900">{lesson.title}</p>
                            <p className="text-xs text-gray-500">{lesson.duration}</p>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Resources */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Resources</h3>
          <div className="space-y-2">
            {courseData.resources.map(resource => (
              <div
                key={resource.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <FileText className="w-4 h-4 text-gray-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{resource.title}</p>
                    {resource.size && <p className="text-xs text-gray-500">{resource.size}</p>}
                  </div>
                </div>
                <button className="p-1 text-gray-600 hover:text-gray-900">
                  <Download className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-2">
          <button
            onClick={() => setShowNotes(!showNotes)}
            className="w-full flex items-center space-x-3 p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Edit3 className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">Notes</span>
          </button>
          <button
            onClick={() => setShowDiscussion(!showDiscussion)}
            className="w-full flex items-center space-x-3 p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <MessageSquare className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">Discussion</span>
          </button>
          <button
            onClick={() => setShowQuiz(!showQuiz)}
            className="w-full flex items-center space-x-3 p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <FileText className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">Quiz</span>
          </button>
        </div>
      </div>
    </div>
  );

  const NotesPanel = () => {
    const noteId = `${currentChapter}-${currentLesson}`;
    const lessonNotes = notes[noteId] || [];

    return (
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mt-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Notes</h3>

        <div className="mb-4">
          <textarea
            ref={notesRef}
            value={currentNote}
            onChange={e => setCurrentNote(e.target.value)}
            placeholder="Add your notes here..."
            className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="flex justify-end mt-2">
            <button
              onClick={saveNote}
              disabled={!currentNote.trim()}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="w-4 h-4" />
              <span>Save Note</span>
            </button>
          </div>
        </div>

        {lessonNotes.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Previous Notes</h4>
            {lessonNotes.map(note => (
              <div key={note.id} className="p-3 bg-gray-50 rounded-lg">
                <p className="text-gray-700 mb-2">{note.text}</p>
                <p className="text-xs text-gray-500">{new Date(note.timestamp).toLocaleString()}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const DiscussionPanel = () => (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mt-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Discussion</h3>

      <div className="space-y-4">
        {courseData.discussions.map(discussion => (
          <div key={discussion.id} className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">{discussion.user.charAt(0)}</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium text-gray-900">{discussion.user}</span>
                  <span className="text-sm text-gray-500">{discussion.time}</span>
                </div>
                <p className="text-gray-700 mb-2">{discussion.message}</p>
                <button className="text-sm text-blue-600 hover:text-blue-700">
                  {discussion.replies} replies
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <textarea
          placeholder="Join the discussion..."
          className="w-full h-20 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <div className="flex justify-end mt-2">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Post Comment
          </button>
        </div>
      </div>
    </div>
  );

  const QuizPanel = () => (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mt-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Assessment</h3>

      <div className="space-y-4">
        <div className="p-4 border border-gray-200 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-3">
            What are the four chambers of the heart?
          </h4>
          <div className="space-y-2">
            {[
              'Left atrium, right atrium, left ventricle, right ventricle',
              'Left atrium, right atrium, left ventricle, right auricle',
              'Superior atrium, inferior atrium, left ventricle, right ventricle',
              'Left chamber, right chamber, upper ventricle, lower ventricle',
            ].map((option, index) => (
              <label key={index} className="flex items-center space-x-3 cursor-pointer">
                <input type="radio" name="quiz1" className="text-blue-600" />
                <span className="text-gray-700">{option}</span>
              </label>
            ))}
          </div>
        </div>

        <button className="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors">
          Submit Answer
        </button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
            <h1 className="text-xl font-bold text-gray-900">{courseData.title}</h1>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search content..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button className="p-2 text-gray-600 hover:text-gray-900">
              <Share2 className="w-5 h-5" />
            </button>
          </div>
        </div>
      </header>

      <div className="flex">
        <Sidebar />

        {/* Main Content */}
        <main className="flex-1 p-6">
          <Breadcrumb />
          <ProgressBar />
          <ContentRenderer />

          {showNotes && <NotesPanel />}
          {showDiscussion && <DiscussionPanel />}
          {showQuiz && <QuizPanel />}
        </main>
      </div>
    </div>
  );
};
